#!/usr/bin/env python3
import re
import os
import requests
import time
from urllib.parse import urlparse

def extract_arxiv_links_and_info(readme_file):
    """Extract arXiv links with their titles and determine target folders"""
    with open(readme_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match arXiv links with titles
    pattern = r'\+ \[([^\]]+)\]\(https://arxiv\.org/abs/([^)]+)\)'
    matches = re.findall(pattern, content)
    
    papers = []
    current_section = "Main"
    current_subsection = ""
    
    lines = content.split('\n')
    for i, line in enumerate(lines):
        # Check for main sections
        if line.strip() == "## Main":
            current_section = "Main"
            current_subsection = ""
        elif line.strip() == "## Applications":
            current_section = "Applications"
            current_subsection = ""
        
        # Check for subsections
        elif line.startswith("### "):
            current_subsection = line[4:].strip()
        
        # Check for arXiv links
        match = re.search(r'\+ \[([^\]]+)\]\(https://arxiv\.org/abs/([^)]+)\)', line)
        if match:
            title = match.group(1)
            arxiv_id = match.group(2)
            
            # Determine target folder
            if current_section == "Main" and current_subsection:
                # Map subsection names to folder names
                subsection_mapping = {
                    "Early Work": "Early Work",
                    "Gradient/Trajectory Matching Surrogate Objective": "Gradient-Trajectory Matching Surrogate Objective",
                    "Distribution/Feature Matching Surrogate Objective": "Distribution-Feature Matching Surrogate Objective",
                    "Kernel-Based Distillation": "Kernel-Based Distillation",
                    "Distilled Dataset Parametrization": "Distilled Dataset Parametrization",
                    "Generative Distillation": "Generative Distillation",
                    "Better Optimization": "Better Optimization",
                    "Better Understanding": "Better Understanding",
                    "Label Distillation": "Label Distillation",
                    "Dataset Quantization": "Dataset Quantization",
                    "Decoupled Distillation": "Decoupled Distillation",
                    "Multimodal Distillation": "Multimodal Distillation",
                    "Self-Supervised Distillation": "Self-Supervised Distillation",
                    "Universal Distillation": "Universal Distillation",
                    "Benchmark": "Benchmark",
                    "Survey": "Survey",
                    "Ph.D. Thesis": "Ph.D. Thesis",
                    "Workshop": "Workshop",
                    "Challenge": "Challenge",
                    "Ranking": "Ranking"
                }
                folder = f"Main/{subsection_mapping.get(current_subsection, current_subsection)}"
            elif current_section == "Applications" and current_subsection:
                folder = f"Applications/{current_subsection}"
            else:
                folder = current_section
            
            papers.append({
                'title': title,
                'arxiv_id': arxiv_id,
                'folder': folder,
                'url': f"https://arxiv.org/pdf/{arxiv_id}.pdf"
            })
    
    return papers

def sanitize_filename(title):
    """Sanitize title for use as filename"""
    # Remove or replace problematic characters
    title = re.sub(r'[<>:"/\\|?*]', '_', title)
    title = re.sub(r'\s+', '_', title)
    title = title.strip('_')
    # Limit length
    if len(title) > 100:
        title = title[:100]
    return title

def download_paper(paper, max_retries=3):
    """Download a single paper with retry logic"""
    folder = paper['folder']
    title = sanitize_filename(paper['title'])
    filename = f"{title}.pdf"
    filepath = os.path.join(folder, filename)
    
    # Skip if file already exists
    if os.path.exists(filepath):
        print(f"Skipping {filename} (already exists)")
        return True
    
    print(f"Downloading: {paper['title']}")
    print(f"  arXiv ID: {paper['arxiv_id']}")
    print(f"  Target: {filepath}")
    
    for attempt in range(max_retries):
        try:
            response = requests.get(paper['url'], timeout=30)
            response.raise_for_status()
            
            # Ensure directory exists
            os.makedirs(folder, exist_ok=True)
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"  ✓ Downloaded successfully")
            return True
            
        except Exception as e:
            print(f"  ✗ Attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retry
    
    print(f"  ✗ Failed to download after {max_retries} attempts")
    return False

def main():
    readme_file = "README (3).md"
    
    print("Extracting arXiv links from README...")
    papers = extract_arxiv_links_and_info(readme_file)
    
    print(f"Found {len(papers)} papers with arXiv links")
    
    successful_downloads = 0
    failed_downloads = 0
    
    for i, paper in enumerate(papers, 1):
        print(f"\n[{i}/{len(papers)}]")
        if download_paper(paper):
            successful_downloads += 1
        else:
            failed_downloads += 1
        
        # Add small delay between downloads to be respectful
        time.sleep(1)
    
    print(f"\n=== Download Summary ===")
    print(f"Total papers found: {len(papers)}")
    print(f"Successfully downloaded: {successful_downloads}")
    print(f"Failed downloads: {failed_downloads}")
    print(f"Already existed: {len(papers) - successful_downloads - failed_downloads}")

if __name__ == "__main__":
    main()
